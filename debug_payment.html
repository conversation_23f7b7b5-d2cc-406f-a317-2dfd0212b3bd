<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-result { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 12px 24px; margin: 8px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        #log { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; max-height: 500px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .json-display { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; white-space: pre-wrap; }
        h1, h2, h3 { color: #333; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 支付系统调试页面</h1>
        
        <div class="test-result info">
            <h3>📊 当前访问信息</h3>
            <p><strong>访问域名:</strong> <span id="currentDomain"></span></p>
            <p><strong>访问协议:</strong> <span id="currentProtocol"></span></p>
            <p><strong>完整URL:</strong> <span id="currentUrl"></span></p>
            <p><strong>Referer:</strong> <span id="referer"></span></p>
            <p><strong>User Agent:</strong> <span id="userAgent" style="font-size: 11px;"></span></p>
        </div>

        <div>
            <h3>🧪 调试测试</h3>
            <button onclick="testDebugCreateOrder()">调试创建订单接口</button>
            <button onclick="testRealCreateOrder()">测试真实创建订单</button>
            <button onclick="testDirectAPI()">直接测试第三方API</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div>
            <h3>📝 测试日志</h3>
            <div id="log"></div>
        </div>
    </div>

    <script>
        // 显示当前访问信息
        document.getElementById('currentDomain').textContent = window.location.hostname;
        document.getElementById('currentProtocol').textContent = window.location.protocol;
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('referer').textContent = document.referrer || '无';
        document.getElementById('userAgent').textContent = navigator.userAgent;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleString();
            const logEntry = document.createElement('div');
            logEntry.className = `test-result ${type}`;
            
            let icon = '📝';
            if (type === 'success') icon = '✅';
            else if (type === 'error') icon = '❌';
            else if (type === 'warning') icon = '⚠️';
            
            logEntry.innerHTML = `<strong>[${timestamp}] ${icon}</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 调试创建订单接口
        async function testDebugCreateOrder() {
            log('开始调试创建订单接口...', 'info');
            
            try {
                const response = await fetch('/Index/Utils/debugCreateOrder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        customer_contact: '<EMAIL>',
                        product_id: '85',
                        pay_type: 'wxpay'
                    })
                });

                const data = await response.json();
                
                if (data.status === 'success') {
                    log('✅ 调试检查通过！', 'success');
                } else {
                    log(`❌ 调试检查失败: ${data.message}`, 'error');
                }
                
                if (data.debug) {
                    log(`调试信息: <div class="json-display">${JSON.stringify(data.debug, null, 2)}</div>`, 'info');
                }
            } catch (error) {
                log(`🚫 调试接口请求失败: ${error.message}`, 'error');
            }
        }

        // 测试真实创建订单
        async function testRealCreateOrder() {
            log('开始测试真实创建订单...', 'info');
            
            try {
                const response = await fetch('/Index/Utils/createOrder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        customer_contact: '<EMAIL>',
                        product_id: '85',
                        pay_type: 'wxpay'
                    })
                });

                const data = await response.json();
                
                if (data.status === 'success') {
                    log(`✅ 订单创建成功！订单号: ${data.data.order_info.order_id}`, 'success');
                    log(`💰 商品: ${data.data.order_info.product_name}, 价格: ${data.data.order_info.product_price}`, 'info');
                } else {
                    log(`❌ 订单创建失败: ${data.message || '未知错误'}`, 'error');
                    if (data.suggestion) {
                        log(`💡 建议: ${data.suggestion}`, 'warning');
                    }
                }
                
                // 显示完整响应
                log(`完整响应: <div class="json-display">${JSON.stringify(data, null, 2)}</div>`, 'info');
            } catch (error) {
                log(`🚫 创建订单请求失败: ${error.message}`, 'error');
            }
        }

        // 直接测试第三方API
        async function testDirectAPI() {
            log('开始直接测试第三方API...', 'info');
            
            try {
                const apiUrl = 'https://cloudshop.qnm6.top/create_order.php?' + new URLSearchParams({
                    customer_contact: '<EMAIL>',
                    product_id: '85',
                    pay_type: 'wxpay'
                });
                
                log(`请求URL: ${apiUrl}`, 'info');
                
                const response = await fetch(apiUrl);
                const data = await response.json();
                
                if (data.status === 'success') {
                    log('✅ 第三方API响应正常！', 'success');
                } else {
                    log(`❌ 第三方API返回错误: ${data.message}`, 'error');
                }
                
                log(`第三方API响应: <div class="json-display">${JSON.stringify(data, null, 2)}</div>`, 'info');
            } catch (error) {
                log(`🚫 第三方API请求失败: ${error.message}`, 'error');
                log('这可能是由于CORS限制导致的，属于正常现象', 'warning');
            }
        }

        // 页面加载时自动记录访问信息
        window.onload = function() {
            log('🚀 调试页面加载完成', 'success');
            const accessType = window.location.hostname === 'lhzs.lol' ? '域名访问 (lhzs.lol)' : 
                              window.location.hostname.match(/^\d+\.\d+\.\d+\.\d+$/) ? 'IP访问' : '其他访问方式';
            log(`访问方式: ${accessType}`, 'info');
            
            // 自动运行调试检查
            setTimeout(() => {
                log('自动运行调试检查...', 'info');
                testDebugCreateOrder();
            }, 1000);
        };
    </script>
</body>
</html>
