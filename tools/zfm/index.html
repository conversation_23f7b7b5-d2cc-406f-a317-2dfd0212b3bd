<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>正反查询-猎户助手Lhzs.LOL</title>
    <style>
        /* 隐藏滚动条 */
        html, body {
            overflow-x: hidden;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }
        
        /* 隐藏 Webkit 浏览器的滚动条 */
        html::-webkit-scrollbar,
        body::-webkit-scrollbar {
            display: none;
        }
        
        /* 保持滚动功能但隐藏滚动条 */
        html {
            overflow-y: scroll;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        html::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }

        
        /* 页面过渡效果 */
        .page-transition {
            transition: all 0.3s ease-in-out;
        }
        
        .fade-out {
            opacity: 0;
            transform: translateY(-10px);
        }
        
        .back-btn {
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }
        
        .back-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s ease;
        }
        
        .back-btn:hover::before {
            left: 100%;
        }
        
        .back-btn:active {
            transform: scale(0.98);
        }
        
        /* 点击波纹效果 */
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
* {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: url('https://www.loliapi.com/acg/pe/') center/cover no-repeat fixed;
            min-height: 100vh;
            padding: 16px;
            margin: 0;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
            -webkit-text-size-adjust: 100%;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
            touch-action: manipulation;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.85);
            z-index: -1;
        }

        .top-bar {
            background: url('https://www.loliapi.com/acg/pc/') center/cover no-repeat;
            border: 3px solid #333;
            border-radius: 8px;
            padding: 20px 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 480px;
            width: calc(100% - 32px);
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 16px;
            box-shadow: 4px 4px 0px #333;
            min-height: 120px;
            position: relative;
        
            box-sizing: border-box;}

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            backdrop-filter: blur(2px);
        }

        .pixel-icons {
            display: flex;
            gap: 12px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .pixel-icon {
            width: 24px;
            height: 24px;
            background: #ff6b35;
            border: 2px solid #333;
            position: relative;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .pixel-icon.orange {
            background: #ff6b35;
        }

        .pixel-icon.gray {
            background: #999;
        }

        .pixel-icon.green {
            background: #4CAF50;
        }

        .pixel-icon.blue {
            background: #2196F3;
            position: relative;
            z-index: 2;
        }

        .pixel-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
        }

        .header-card {
            background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            max-width: 480px;
            width: calc(100% - 32px);
            margin-left: auto;
            margin-right: auto;
            position: relative;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(5px);
        
            box-sizing: border-box;}

        .app-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 16px 0;
            text-shadow: 2px 2px 0px rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-title::before {
            content: '🔄';
            font-size: 16px;
        }

        .back-btn {
            background: rgba(0,0,0,0.3);
            color: white;
            border: 3px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.1s ease;
            text-shadow: 1px 1px 0px rgba(0,0,0,0.5);
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.5);
            transform: translate(1px, 1px);
            box-shadow: 1px 1px 0px rgba(0,0,0,0.3);
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            max-width: 480px;
            width: calc(100% - 32px);
            margin: 0 auto;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(10px);
        
            box-sizing: border-box;}

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
        }

        .input-group {
            margin-bottom: 16px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 6px;
            color: #333;
            font-weight: bold;
            font-size: 14px;
        }

        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 3px solid #333;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: all 0.1s ease;
            outline: none;
            background: #f9f9f9;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
        }

        input[type="text"]:focus, input[type="password"]:focus {
            background: white;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.2);
        }

        button {
            background: #607D8B;
            color: white;
            border: 3px solid #333;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            cursor: pointer;
            transition: all 0.1s ease;
            margin: 6px 4px;
            box-shadow: 3px 3px 0px #333;
        }

        button:hover {
            transform: translate(1px, 1px);
            box-shadow: 2px 2px 0px #333;
        }

        button:active {
            transform: translate(2px, 2px);
            box-shadow: 1px 1px 0px #333;
        }

        .query-btn {
            width: 100%;
        }

        .result-container {
            margin-top: 20px;
            padding: 20px;
            background: rgba(249, 249, 249, 0.95);
            border: 3px solid #333;
            border-radius: 8px;
            display: none;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
            backdrop-filter: blur(5px);
        }

        .result-container.show {
            display: block;
        }

        .images-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-top: 16px;
        }

        .image-section {
            text-align: center;
        }

        .image-title {
            font-size: 14px;
            font-weight: bold;
            color: #607D8B;
            margin-bottom: 12px;
            padding: 8px;
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
        }

        .result-image {
            max-width: 100%;
            border: 3px solid #333;
            border-radius: 8px;
            box-shadow: 4px 4px 0px #333;
            margin: 8px 0;
            background: white;
        }

        .loading {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            color: #666;
            animation: pulse 1.5s infinite;
            padding: 40px;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .error {
            color: #f44336;
            margin-top: 10px;
            font-size: 12px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            text-align: center;
        }

        .warning {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #856404;
            text-align: center;
        }

        .success-msg {
            color: #4CAF50;
            font-weight: bold;
            text-align: center;
            margin-bottom: 16px;
        }

        @media (max-width: 480px) {
            .top-bar {
                width: calc(100% - 16px);
                margin: 0 8px 12px 8px;
                padding: 16px 12px;
                min-height: 100px;
            }
            
            .header-card {
                width: calc(100% - 16px);
                margin: 0 8px 12px 8px;
                padding: 16px;
            }
            
            .container {
                width: calc(100% - 16px);
                margin: 0 8px;
                padding: 16px;
            }
            
            .app-title {
                font-size: 16px;
            }
            
            h1 {
                font-size: 18px;
            }
            
            button {
                width: 100%;
                margin: 4px 0;
            }

            .images-container {
                gap: 16px;
            }
        }

        @media (min-width: 600px) {
            .images-container {
                grid-template-columns: 1fr 1fr;
            }
        }
    </style>
</head>
<body class="page-transition">
    <div class="top-bar">
        <div class="pixel-icons">
            <div class="pixel-icon orange"></div>
            <div class="pixel-icon gray"></div>
            <div class="pixel-icon green"></div>
        </div>
        <div class="pixel-icon blue"></div>
    </div>
    
    <div class="header-card">
        <h2 class="app-title">正反查询-猎户助手Lhzs.LOL</h2>
        <button class="back-btn" onclick="goBack()">返回首页</button>
    </div>
    
    <div class="container">
        <h1>🔄 正反查询</h1>
        
        <div class="warning">
            ⚠️ 请确保输入信息准确，仅用于合法用途
        </div>
        
        <div class="input-group">
            <label for="nameInput">姓名：</label>
            <input type="text" id="nameInput" placeholder="请输入真实姓名">
            <div class="error" id="nameError"></div>
        </div>

        <div class="input-group">
            <label for="idInput">身份证号码：</label>
            <input type="text" id="idInput" placeholder="请输入18位身份证号码" maxlength="18">
            <div class="error" id="idError"></div>
        </div>

        <div class="input-group">
            <label for="tokenInput">卡密(Token)：</label>
            <input type="password" id="tokenInput" placeholder="请输入您的Token">
            <div class="error" id="tokenError"></div> 
            <a href="/Index/utils/index" style="color: red; font-weight: bold; font-size: 0.8rem; text-decoration: none;">
                    没有Token?点我购买
                </a>
        </div>

        <button class="query-btn" onclick="queryZFM()">🔍 查询正反面</button>

        <div class="result-container" id="resultContainer">
            <div id="loadingMsg" class="loading" style="display: none;">正在查询身份证正反面...</div>
            <div id="resultContent"></div>
        </div>
        
        <div class="error" id="errorMsg"></div>
    </div>

    <script>
        let isQuerying = false;

        // 验证身份证号码格式
        function validateIdCard(idCard) {
            const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
            return idCardRegex.test(idCard);
        }

        // 验证姓名格式
        function validateName(name) {
            const nameRegex = /^[\u4e00-\u9fa5]{2,10}$/;
            return nameRegex.test(name);
        }

        // 查询正反面信息
        async function queryZFM() {
            if (isQuerying) return;
            
            const nameInput = document.getElementById('nameInput');
            const idInput = document.getElementById('idInput');
            const tokenInput = document.getElementById('tokenInput');
            const nameError = document.getElementById('nameError');
            const idError = document.getElementById('idError');
            const tokenError = document.getElementById('tokenError');
            const errorMsg = document.getElementById('errorMsg');
            const resultContainer = document.getElementById('resultContainer');
            const loadingMsg = document.getElementById('loadingMsg');
            const resultContent = document.getElementById('resultContent');
            
            // 清除之前的错误信息
            nameError.textContent = '';
            idError.textContent = '';
            tokenError.textContent = '';
            errorMsg.textContent = '';
            
            const name = nameInput.value.trim();
            const idCard = idInput.value.trim();
            const token = tokenInput.value.trim();
            
            // 验证输入
            let hasError = false;
            
            if (!name) {
                nameError.textContent = '请输入姓名';
                hasError = true;
            } else if (!validateName(name)) {
                nameError.textContent = '请输入正确的中文姓名(2-10个字符)';
                hasError = true;
            }
            
            if (!idCard) {
                idError.textContent = '请输入身份证号码';
                hasError = true;
            } else if (!validateIdCard(idCard)) {
                idError.textContent = '请输入正确的18位身份证号码';
                hasError = true;
            }
            
            if (!token) {
                tokenError.textContent = '请输入Token';
                hasError = true;
            }
            
            if (hasError) {
                return;
            }
            
            isQuerying = true;
            
            // 显示加载状态
            loadingMsg.style.display = 'block';
            resultContent.innerHTML = '';
            resultContainer.classList.add('show');
            
            try {
                const url = `https://api.qnm6.top/api/zfm/?xm=${encodeURIComponent(name)}&hm=${encodeURIComponent(idCard)}&token=${encodeURIComponent(token)}`;
                
                const response = await fetch(url);
                const data = await response.json();
                
                loadingMsg.style.display = 'none';
                
                if (data.code === '200' || data.code === 200) {
                    let imagesHtml = '';
                    
                    if (data.imgurl || data.back_imgurl) {
                        imagesHtml = '<div class="images-container">';
                        
                        // 身份证正面
                        if (data.back_imgurl) {
                            imagesHtml += `
                                <div class="image-section">
                                    <div class="image-title">📄 身份证正面</div>
                                    <img src="${data.back_imgurl}" alt="身份证正面" class="result-image" onerror="this.parentElement.style.display='none'; showImageError('正面图片');">
                                </div>
                            `;
                        }
                        
                        // 身份证反面
                        if (data.imgurl) {
                            imagesHtml += `
                                <div class="image-section">
                                    <div class="image-title">📄 身份证反面</div>
                                    <img src="${data.imgurl}" alt="身份证反面" class="result-image" onerror="this.parentElement.style.display='none'; showImageError('反面图片');">
                                </div>
                            `;
                        }
                        
                        imagesHtml += '</div>';
                    }
                    
                    resultContent.innerHTML = `
                        <div class="success-msg">✅ ${data.message || '查询成功'}</div>
                        ${imagesHtml}
                    `;
                } else {
                    throw new Error(data.message || '查询失败');
                }
                
            } catch (error) {
                loadingMsg.style.display = 'none';
                resultContainer.classList.remove('show');
                errorMsg.textContent = '查询失败: ' + error.message;
                console.error('查询正反面失败:', error);
            }
            
            isQuerying = false;
        }

        // 显示图片错误
        function showImageError(imageType) {
            const errorMsg = document.getElementById('errorMsg');
            if (errorMsg.textContent === '') {
                errorMsg.textContent = imageType + '加载失败';
            }
        }

        // 返回根目录
        

        // 回车键查询
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                queryZFM();
            }
        });

        // 页面加载完成后聚焦到姓名输入框
        window.addEventListener('load', function() {
            document.getElementById('nameInput').focus();
        });
    
        // 防止页面缩放
        document.addEventListener("gesturestart", function (e) {
            e.preventDefault();
        });
        
        document.addEventListener("gesturechange", function (e) {
            e.preventDefault();
        });
        
        document.addEventListener("gestureend", function (e) {
            e.preventDefault();
        });
        
        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener("touchend", function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // 防止键盘快捷键缩放
        document.addEventListener("keydown", function(e) {
            if ((e.ctrlKey || e.metaKey) && (e.keyCode === 61 || e.keyCode === 107 || e.keyCode === 173 || e.keyCode === 109 || e.keyCode === 187 || e.keyCode === 189)) {
                e.preventDefault();
            }
        });
        
        // 防止鼠标滚轮缩放
        document.addEventListener("wheel", function(e) {
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
            }
        }, { passive: false });
        // 增强的返回函数，带过渡效果
        function goBack() {
            // 添加淡出效果
            document.body.classList.add('fade-out');
            
            // 延迟跳转，让动画完成
            setTimeout(function() {
                window.location.href = '/';
            }, 300);
        }
        
        // 添加按钮点击波纹效果
        function addRippleEffect(event) {
            const button = event.currentTarget;
            const ripple = document.createElement('div');
            const rect = button.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = event.clientX - rect.left - size / 2;
            const y = event.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            button.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            // 为返回按钮添加波纹效果
            const backBtn = document.querySelector('.back-btn');
            if (backBtn) {
                backBtn.addEventListener('click', addRippleEffect);
            }
            
            // 页面进入动画
            document.body.style.opacity = '0';
            document.body.style.transform = 'translateY(10px)';
            
            setTimeout(function() {
                document.body.style.transition = 'all 0.3s ease-in-out';
                document.body.style.opacity = '1';
                document.body.style.transform = 'translateY(0)';
            }, 50);
        });
        
        // 防止页面后退时的闪烁
        window.addEventListener('pageshow', function(event) {
            if (event.persisted) {
                document.body.classList.remove('fade-out');
                document.body.style.opacity = '1';
                document.body.style.transform = 'translateY(0)';
            }
        });

    </script>

<script>
var _mtj = _mtj || [];
(function () {
    var mtj = document.createElement("script");
    mtj.src = "https://node94.aizhantj.com:21233/tjjs/?k=17jzo46p2e5";
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(mtj, s);
})();
</script>
</body>
</html>
